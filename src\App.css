.App {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f4f6fa;
}

.eshop-header {
  background: #1976d2;
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 18px 32px;
  font-size: 1.4rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
}

.eshop-title {
  letter-spacing: 1px;
}

.eshop-nav-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.eshop-cart {
  font-size: 1.1rem;
  cursor: pointer;
}

.login-btn, .logout-btn {
  background: #fff;
  color: #1976d2;
  border: 2px solid #fff;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.login-btn:hover, .logout-btn:hover {
  background: transparent;
  color: #fff;
}

.logout-btn {
  background: #f44336;
  color: #fff;
  border-color: #f44336;
}

.logout-btn:hover {
  background: transparent;
  color: #f44336;
}

/* Login Form Styles */
.login-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.login-form {
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  width: 90%;
  max-width: 400px;
}

.login-form h2 {
  margin: 0 0 20px 0;
  color: #333;
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #555;
  font-weight: 600;
}

.form-group input {
  width: 100%;
  padding: 12px;
  border: 2px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus {
  outline: none;
  border-color: #1976d2;
}

.form-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.submit-btn, .cancel-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.submit-btn {
  background: #1976d2;
  color: #fff;
}

.submit-btn:hover {
  background: #1565c0;
}

.cancel-btn {
  background: #f5f5f5;
  color: #666;
}

.cancel-btn:hover {
  background: #e0e0e0;
}

/* Profile Card Styles */
.profile-card {
  background: #fff;
  margin: 20px auto;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 500px;
  border-left: 4px solid #1976d2;
}

.profile-card h3 {
  margin: 0 0 15px 0;
  color: #1976d2;
  font-size: 1.5rem;
}

.profile-info p {
  margin: 8px 0;
  color: #555;
  font-size: 1rem;
}

.profile-info strong {
  color: #333;
}

main {
  flex: 1 0 auto;
  width: 100%;
  margin: 0 auto;
  background: transparent;
}

.eshop-footer {
  background: #222;
  color: #fff;
  text-align: center;
  padding: 14px 0;
  font-size: 1rem;
  letter-spacing: 0.5px;
  margin-top: 32px;
}

@media (max-width: 600px) {
  .eshop-header {
    flex-direction: column;
    gap: 8px;
    padding: 10px 8px;
    font-size: 1.1rem;
  }

  .eshop-nav-right {
    gap: 15px;
  }

  .login-btn, .logout-btn {
    padding: 6px 12px;
    font-size: 0.9rem;
  }

  .login-form {
    padding: 20px;
    margin: 20px;
  }

  .profile-card {
    margin: 15px;
    padding: 20px;
  }

  .eshop-footer {
    font-size: 0.95rem;
    padding: 10px 0;
  }
}
