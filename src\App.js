import { useState } from 'react';
import './App.css';
import ProductList from './ProductList';

function App() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [showLoginForm, setShowLoginForm] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [loginData, setLoginData] = useState({
    username: '',
    password: ''
  });

  const handleLoginClick = () => {
    setShowLoginForm(true);
  };

  const handleLoginSubmit = (e) => {
    e.preventDefault();
    // Simple validation - in real app, you'd validate against a backend
    if (loginData.username && loginData.password) {
      setUserProfile({
        username: loginData.username,
        email: `${loginData.username}@example.com`,
        joinDate: new Date().toLocaleDateString()
      });
      setIsLoggedIn(true);
      setShowLoginForm(false);
      setLoginData({ username: '', password: '' });
    } else {
      alert('Please enter both username and password');
    }
  };

  const handleLogout = () => {
    setIsLoggedIn(false);
    setUserProfile(null);
  };

  const handleInputChange = (e) => {
    setLoginData({
      ...loginData,
      [e.target.name]: e.target.value
    });
  };

  const closeLoginForm = () => {
    setShowLoginForm(false);
    setLoginData({ username: '', password: '' });
  };

  return (
    <div className="App">
      <nav className="eshop-header">
        <div className="eshop-title">My E-Shop</div>
        <div className="eshop-nav-right">
          <div className="eshop-cart">
            <span role="img" aria-label="cart">🛒</span> Cart
          </div>
          {!isLoggedIn ? (
            <button className="login-btn" onClick={handleLoginClick}>
              Login
            </button>
          ) : (
            <button className="logout-btn" onClick={handleLogout}>
              Logout
            </button>
          )}
        </div>
      </nav>

      {showLoginForm && (
        <div className="login-overlay">
          <div className="login-form">
            <h2>Login</h2>
            <form onSubmit={handleLoginSubmit}>
              <div className="form-group">
                <label htmlFor="username">Username:</label>
                <input
                  type="text"
                  id="username"
                  name="username"
                  value={loginData.username}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-group">
                <label htmlFor="password">Password:</label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={loginData.password}
                  onChange={handleInputChange}
                  required
                />
              </div>
              <div className="form-buttons">
                <button type="submit" className="submit-btn">Login</button>
                <button type="button" className="cancel-btn" onClick={closeLoginForm}>Cancel</button>
              </div>
            </form>
          </div>
        </div>
      )}

      {isLoggedIn && userProfile && (
        <div className="profile-card">
          <h3>Welcome, {userProfile.username}!</h3>
          <div className="profile-info">
            <p><strong>Username:</strong> {userProfile.username}</p>
            <p><strong>Email:</strong> {userProfile.email}</p>
            <p><strong>Member since:</strong> {userProfile.joinDate}</p>
          </div>
        </div>
      )}

      <main>
        <h1>Hello Hama</h1>
        <ProductList />
      </main>
      <footer className="eshop-footer">
        &copy; {new Date().getFullYear()} My E-Shop. All rights reserved.
      </footer>
    </div>
  );
}

export default App;
